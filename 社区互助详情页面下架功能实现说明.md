# 社区互助详情页面下架功能实现说明

## 功能概述

参考商品详情页面的下架功能实现，为社区互助详情页面添加了下架/上架功能。当发布者是当前用户时，会显示相应的操作按钮。

## 实现内容

### 1. 前端页面修改

#### 1.1 JavaScript逻辑 (index.js)

**新增数据字段：**
```javascript
data: {
  // 下架/上架确认弹框
  showOfflineDialog: false,
  showOnlineDialog: false
}
```

**新增方法：**
- `statusConfig` - 状态更新配置对象
- `handleStatusAction(type, action)` - 通用状态处理方法
- `onStatusButtonTap(e)` - 通用状态按钮点击事件处理器
- `onOffline()` - 点击下架按钮
- `onOnline()` - 点击重新上架按钮
- `onOfflineDialogClose()` - 下架确认弹窗关闭
- `onOnlineDialogClose()` - 重新上架确认弹窗关闭
- `onOfflineConfirm()` - 确认下架互助
- `onOnlineConfirm()` - 确认重新上架互助
- `updateHelpStatus(status, actionName, successMessage)` - 通用状态更新方法

#### 1.2 模板文件 (index.wxml)

**按钮显示逻辑：**
```xml
<view wx:else class="action-button-group">
  <!-- 根据互助状态显示不同的按钮 -->
  <view wx:if="{{helpDetail.status == '3'}}" class="action-button online" bindtap="onOnline" hover-class="button-hover">
    <text>重新上架</text>
  </view>
  <view wx:else class="action-button offline" bindtap="onOffline" hover-class="button-hover">
    <text>下架</text>
  </view>
  <view class="action-button edit" bindtap="onEdit" hover-class="button-hover">
    <van-icon name="edit" size="16px" />
    <text>编辑</text>
  </view>
</view>
```

**确认弹框：**
- 下架确认弹框 (`showOfflineDialog`)
- 重新上架确认弹框 (`showOnlineDialog`)

#### 1.3 样式文件 (index.wxss)

**新增样式：**
- `.action-button.offline` - 下架按钮样式（红色）
- `.action-button.online` - 重新上架按钮样式（青色）
- `.offline-dialog-content` - 下架确认弹窗内容样式
- `.online-dialog-content` - 重新上架确认弹窗内容样式

### 2. 后端接口

**接口路径：** `PUT /community-help/update-status/{id}`

**请求参数：**
```json
{
  "status": "1" // 1-上架, 3-下架
}
```

**状态枚举：**
- `ProductStatus.ON_SALE("1", "在售")` - 上架状态
- `ProductStatus.OFF_SHELF("3", "下架")` - 下架状态

### 3. API调用

**前端API方法：**
```javascript
// 更新互助状态
updateCommunityHelpStatus(id, status) {
    return request.put(`/community-help/update-status/${id}`, { status })
}
```

## 功能特点

### 1. 权限控制
- 只有发布者本人才能看到下架/上架按钮
- 后端接口会验证用户身份，确保只有发布者可以操作

### 2. 状态管理
- 状态为 "3"（下架）时显示"重新上架"按钮
- 状态为 "1"（上架）时显示"下架"按钮
- 操作成功后自动刷新页面数据

### 3. 用户体验
- 操作前显示确认弹框，避免误操作
- 操作过程中显示加载状态
- 操作完成后显示成功提示
- 网络异常时显示错误提示

### 4. 代码复用
- 参考商品详情页面的实现模式
- 使用通用的状态处理方法
- 保持与现有代码风格一致

## 使用场景

1. **下架互助**：发布者发现互助信息有误或不再需要时，可以下架隐藏
2. **重新上架**：发布者修正信息后，可以重新上架让其他用户看到
3. **临时隐藏**：发布者临时不希望其他用户看到互助信息时使用

## 注意事项

1. 下架后的互助信息其他用户无法在列表中看到
2. 下架状态的互助信息无法被联系
3. 发布者仍可以通过"我的发布"页面管理下架的互助
4. 状态更新会立即生效，无需审核
