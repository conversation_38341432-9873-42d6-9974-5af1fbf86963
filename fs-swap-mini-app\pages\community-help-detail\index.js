const api = require('../../config/api.js')
const { ensureLogin } = require('../../utils/user.js')
const systemInfoService = require('../../services/systemInfo.js')
const dateUtil = require('../../utils/dateUtil')

Page({
  data: {
    helpId: null,
    helpDetail: null,
    isMyHelp: false,
    contactLoading: false,
    categories: [], // 分类列表
    helpSilver: '5', // 互助服务费用，默认值
    // 联系确认弹框
    showContactConfirmDialog: false,
    // 联系结果弹框
    showContactResultDialog: false,
    contactResultTitle: '',
    // 订单相关
    orderId: null,
    // 联系方式弹框
    showContactModal: false,
    contacts: [],
    targetNickname: '',
    fileUrl: '',
    // 下架/上架确认弹框
    showOfflineDialog: false,
    showOnlineDialog: false
  },

  onLoad(options) {
    // 初始化文件服务器地址
    const systemInfo = wx.getStorageSync('systemInfo')
    this.setData({
      fileUrl: systemInfo?.fileUrl || ''
    })

    // 加载互助服务费用
    this.loadHelpSilver()

    // 启用分享功能
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
    })

    if (options.id) {
      this.setData({ helpId: options.id })
      // 先加载分类，再加载详情
      this.loadCategories().then(() => {
        this.loadHelpDetail()
      })
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  onShow() {
    // 检查是否需要刷新数据
    const needRefresh = wx.getStorageSync('needRefreshHelpDetail')
    if (needRefresh && this.data.helpId) {
      // 清除刷新标记
      wx.removeStorageSync('needRefreshHelpDetail')
      // 重新加载数据
      this.loadCategories().then(() => {
        this.loadHelpDetail()
      })
    }
  },

  onPullDownRefresh() {
    // 先加载分类，再加载详情
    this.loadCategories().then(() => {
      this.loadHelpDetail()
    })
  },

  /**
   * 加载分类列表
   */
  async loadCategories() {
    try {
      // 使用统一的系统信息服务获取分类数据
      const categories = await systemInfoService.getCommunityHelpRequireCategories()
      this.setData({ categories })
      return Promise.resolve()
    } catch (error) {
      // 分类加载失败不影响主要功能，设置空数组
      this.setData({ categories: [] })
      return Promise.resolve()
    }
  },

  /**
   * 加载互助服务费用
   */
  async loadHelpSilver() {
    try {
      const helpSilver = await systemInfoService.getHelpSilver()
      this.setData({ helpSilver })
    } catch (error) {
      console.error('获取互助服务费用失败:', error)
      // 使用默认值
      this.setData({ helpSilver: '5' })
    }
  },

  /**
   * 加载互助详情
   */
  async loadHelpDetail() {
    try {
      wx.showLoading({ title: '加载中...' })

      const response = await api.getCommunityHelpDetail(this.data.helpId)

      // 提取data字段中的实际数据
      const helpDetail = response.data

      wx.hideLoading()
      wx.stopPullDownRefresh()

      // 处理图片数据（逗号分隔格式）
      const images = helpDetail.images ? helpDetail.images.split(',').filter(img => img.trim()).map(img => {
        // 如果图片URL不是完整URL，添加文件服务器前缀
        if (!img.startsWith('http')) {
          const systemInfo = wx.getStorageSync('systemInfo')
          return systemInfo?.fileUrl ? systemInfo.fileUrl + img : img
        }
        return img
      }) : []

      // 检查是否是自己发布的
      const userInfo = wx.getStorageSync('userInfo')
      const isMyHelp = userInfo && userInfo.id === helpDetail.userId

      // 获取分类名称
      let categoryName = ''
      if (Array.isArray(this.data.categories) && this.data.categories.length > 0) {
        const category = this.data.categories.find(cat => cat.dictValue === helpDetail.category)
        categoryName = category ? category.dictLabel : ''
      }

      // 确保publishType为字符串类型
      const publishType = typeof helpDetail.publishType === 'number' ? helpDetail.publishType.toString() : helpDetail.publishType

      // 处理头像URL（防御性编程，确保头像URL完整）
      let avatar = helpDetail.avatar
      if (avatar && !avatar.startsWith('http')) {
        const systemInfo = wx.getStorageSync('systemInfo')
        avatar = systemInfo?.fileUrl ? systemInfo.fileUrl + avatar : avatar
      }

      // 处理截止时间显示，只显示到天
      const endTime = dateUtil.formatToDateOnly(helpDetail.endTime)

      this.setData({
        helpDetail: {
          ...helpDetail,
          images,
          categoryName,
          publishType,
          avatar,
          endTime
        },
        isMyHelp
      })
    } catch (error) {
      wx.hideLoading()
      wx.stopPullDownRefresh()

      wx.showModal({
        title: '加载失败',
        content: '无法加载互助详情，请稍后重试',
        showCancel: false,
        success: () => {
          wx.navigateBack()
        }
      })
    }
  },

  /**
   * 预览图片
   */
  onPreviewImage(event) {
    const index = event.currentTarget.dataset.index
    const images = this.data.helpDetail.images
    
    wx.previewImage({
      current: images[index],
      urls: images
    })
  },

  /**
   * 查看用户资料
   */
  onUserProfile() {
    const userId = this.data.helpDetail.userId
    if (userId) {
      wx.navigateTo({
        url: `/pages/user/userProfile/userProfile?userId=${userId}`
      })
    }
  },

  /**
   * 编辑互助
   */
  onEdit() {
    if (!this.data.isMyHelp) {
      wx.showToast({
        title: '只能编辑自己发布的互助',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/community-help-publish/index?id=${this.data.helpId}&mode=edit`
    })
  },

  /**
   * 联系发布者
   */
  onContact() {
    if (this.data.isMyHelp) {
      wx.showToast({
        title: '不能联系自己',
        icon: 'none'
      })
      return
    }

    ensureLogin(this, () => {
      this.showContactConfirm()
    })
  },

  /**
   * 显示联系确认对话框
   */
  showContactConfirm() {
    this.setData({
      showContactConfirmDialog: true
    })
  },

  /**
   * 联系确认弹框关闭
   */
  onContactConfirmDialogClose() {
    this.setData({
      showContactConfirmDialog: false
    })
  },

  /**
   * 确认联系
   */
  onContactConfirm() {
    this.setData({
      showContactConfirmDialog: false
    })
    this.contactPublisher()
  },

  /**
   * 联系发布者
   */
  async contactPublisher() {
    if (this.data.contactLoading) return

    // 显示加载中
    wx.showLoading({
      title: '处理中...',
      mask: true
    })

    try {
      const result = await api.contactCommunityHelp(this.data.helpId)

      wx.hideLoading()

      // 联系成功，显示联系结果弹框
      this.setData({
        contactLoading: false,
        showContactResultDialog: true,
        contactResultTitle: '联系成功',
        orderId: result.data.orderId,
        targetNickname: this.data.helpDetail.nickname || '发布者'
      })

      // 刷新互助详情
      this.loadHelpDetail()
    } catch (error) {
      wx.hideLoading()

      this.setData({
        contactLoading: false,
        showContactResultDialog: true,
        contactResultTitle: '联系失败'
      })
    }
  },

  /**
   * 联系结果弹框关闭
   */
  onContactResultDialogClose() {
    this.setData({
      showContactResultDialog: false
    })
  },

  /**
   * 查看订单
   */
  onViewOrder() {
    this.setData({
      showContactResultDialog: false
    })

    if (this.data.orderId) {
      wx.navigateTo({
        url: `/pages/order/detail?id=${this.data.orderId}`
      })
    } else {
      wx.navigateTo({
        url: '/pages/order/list'
      })
    }
  },

  /**
   * 查看联系方式（从联系结果弹框点击）
   */
  onShowContactInfo() {
    // 关闭联系结果弹框
    this.setData({
      showContactResultDialog: false
    })

    // 如果是联系失败，直接关闭弹框
    if (this.data.contactResultTitle !== '联系成功') {
      return
    }

    // 检查订单ID
    if (!this.data.orderId) {
      wx.showToast({
        title: '订单信息异常',
        icon: 'none'
      })
      return
    }

    // 获取联系方式并显示
    wx.showLoading({
      title: '获取联系方式...',
      mask: true
    })

    api.getOrderUserContact(this.data.orderId).then(res => {
      wx.hideLoading()
      if (res.code === 200 && res.data) {
        // 设置联系方式数据
        this.setData({
          contacts: res.data,
          targetNickname: this.data.helpDetail.nickname || '发布者'
        })
        // 显示联系方式弹框
        this.showContactModal()
      } else {
        wx.showToast({
          title: res.msg || '获取联系方式失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      wx.hideLoading()
      wx.showToast({
        title: '网络异常，请稍后重试',
        icon: 'none'
      })
    })
  },

  /**
   * 显示联系方式弹窗
   */
  showContactModal() {
    if (!this.data.contacts || this.data.contacts.length === 0) {
      wx.showModal({
        title: '联系方式',
        content: `${this.data.targetNickname || '发布者'} 未设置联系方式`,
        showCancel: false,
        confirmText: '确定'
      })
      return
    }

    this.setData({
      showContactModal: true
    })
  },

  /**
   * 关闭联系方式弹窗
   */
  closeContactModal() {
    this.setData({
      showContactModal: false
    })

    // 如果是从联系成功后查看联系方式，关闭后自动跳转到订单详情
    if (this.data.orderId) {
      setTimeout(() => {
        wx.navigateTo({
          url: `/pages/order/detail?id=${this.data.orderId}`,
          fail: () => {
            // 如果跳转失败，跳转到订单列表
            wx.navigateTo({
              url: '/pages/order/list'
            })
          }
        })
      }, 300)
    }
  },

  /**
   * 分享给朋友
   */
  onShareAppMessage() {
    const helpDetail = this.data.helpDetail
    if (!helpDetail) {
      return {
        title: '邻里互助 - 邻里间的温暖互助',
        path: '/pages/community-help/index',
      }
    }

    // 构建分享标题：类型 + 标题
    const typeText = helpDetail.publishType === '1' ? '【需求】' : '【服务】'
    const shareTitle = `${typeText}${helpDetail.title}`

    return {
      title: shareTitle,
      path: `/pages/community-help-detail/index?id=${this.data.helpId}`,
      imageUrl: helpDetail.images && helpDetail.images.length > 0
        ? helpDetail.images[0]
        : '',
      success: function (res) {
        wx.showToast({
          title: '分享成功',
          icon: 'success',
        })
      },
      fail: function (res) {
        // 分享失败处理
      },
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const helpDetail = this.data.helpDetail
    if (!helpDetail) {
      return {
        title: '邻里互助 - 邻里间的温暖互助',
        query: '',
      }
    }

    // 构建分享标题
    const typeText = helpDetail.publishType === '1' ? '需求' : '服务'
    const shareTitle = `邻里${typeText}：${helpDetail.title}`

    return {
      title: shareTitle,
      query: `id=${this.data.helpId}`,
      imageUrl: helpDetail.images && helpDetail.images.length > 0
        ? helpDetail.images[0]
        : '',
    }
  },

  /**
   * 状态更新配置
   */
  statusConfig: {
    offline: {
      dialogKey: 'showOfflineDialog',
      status: '3',
      actionName: '下架',
      successMessage: '互助已下架',
    },
    online: {
      dialogKey: 'showOnlineDialog',
      status: '1',
      actionName: '上架',
      successMessage: '互助已重新上架',
    },
  },

  /**
   * 通用状态处理方法
   */
  handleStatusAction: function (type, action) {
    const config = this.statusConfig[type]
    if (!config) return

    if (action === 'show') {
      this.setData({ [config.dialogKey]: true })
    } else if (action === 'close') {
      this.setData({ [config.dialogKey]: false })
    } else if (action === 'confirm') {
      this.updateHelpStatus(
        config.status,
        config.actionName,
        config.successMessage
      )
    }
  },

  /**
   * 通用状态按钮点击事件处理器
   */
  onStatusButtonTap: function (e) {
    const { type, action } = e.currentTarget.dataset
    this.handleStatusAction(type, action)
  },

  // 保留原有方法以兼容现有模板
  /**
   * 点击下架按钮
   */
  onOffline: function () {
    this.handleStatusAction('offline', 'show')
  },

  /**
   * 点击重新上架按钮
   */
  onOnline: function () {
    this.handleStatusAction('online', 'show')
  },

  /**
   * 下架确认弹窗关闭
   */
  onOfflineDialogClose: function () {
    this.handleStatusAction('offline', 'close')
  },

  /**
   * 重新上架确认弹窗关闭
   */
  onOnlineDialogClose: function () {
    this.handleStatusAction('online', 'close')
  },

  /**
   * 确认下架互助
   */
  onOfflineConfirm: function () {
    this.handleStatusAction('offline', 'confirm')
  },

  /**
   * 确认重新上架互助
   */
  onOnlineConfirm: function () {
    this.handleStatusAction('online', 'confirm')
  },

  /**
   * 通用状态更新方法
   */
  updateHelpStatus: function (status, actionName, successMessage) {
    wx.showLoading({
      title: `${actionName}中...`,
      mask: true,
    })

    api.updateCommunityHelpStatus(this.data.helpDetail.id, status)
      .then((res) => {
        wx.hideLoading()

        if (res.code === 200) {
          // 关闭所有弹窗
          this.setData({
            showOfflineDialog: false,
            showOnlineDialog: false,
          })

          wx.showToast({
            title: successMessage,
            icon: 'success',
            duration: 2000,
          })

          // 重新加载互助详情以更新状态
          this.loadHelpDetail()
        } else {
          this.setData({
            showOfflineDialog: false,
            showOnlineDialog: false,
          })
          wx.showToast({
            title: res.msg || `${actionName}失败`,
            icon: 'none',
          })
        }
      })
      .catch((err) => {
        wx.hideLoading()
        this.setData({
          showOfflineDialog: false,
          showOnlineDialog: false,
        })
        wx.showToast({
          title: '网络异常，请重试',
          icon: 'none',
        })
      })
  }
})
